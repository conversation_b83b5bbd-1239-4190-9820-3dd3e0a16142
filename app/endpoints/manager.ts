import type { EndpointConfig } from '../config/endpoints'
import { Endpoint } from './endpoint'

export class EndpointManager {
    protected endpoints: Endpoint[]

    public constructor(endpoints: EndpointConfig[]) {
        this.endpoints = endpoints.filter((i) => i.enabled).map((i) => this.createEndpoint(i))
    }

    protected createEndpoint(config: EndpointConfig) {
        return new Endpoint(config)
    }
}
